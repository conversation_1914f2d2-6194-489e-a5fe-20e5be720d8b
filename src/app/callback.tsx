/**
 * OAuth Callback Handler
 *
 * Handles OAuth redirects from Okta authentication.
 * Processes the authorization code and displays the result.
 *
 * DEV ONLY !!!
 */

import React, { useEffect, useRef } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as WebBrowser from 'expo-web-browser';

export default function CallbackScreen() {
	const router = useRouter();
	const processedRef = useRef(false);

	useEffect(() => {
		if (processedRef.current) return;
		processedRef.current = true;

		// Complete any pending browser auth session and close the in-app browser
		WebBrowser.maybeCompleteAuthSession();

		if (Platform.OS === 'ios') {
			WebBrowser.dismissBrowser().catch(() => {});
		}

		// Immediately return users to the POC screen
		router.replace('/okta-poc');
	}, [router]);

	return (
		<SafeAreaView style={styles.container} />
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#F9FAFB',
	},
});
