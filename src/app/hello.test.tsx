import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { store } from '@/store';
import HelloScreen from './hello';
import { Alert } from 'react-native';
import { DefaultTheme } from '@/theme';

jest.spyOn(Alert, 'alert');

let mockSetOptions: jest.Mock;

jest.mock('expo-router', () => {
	mockSetOptions = jest.fn(); //
	return {
		useNavigation: () => ({
			setOptions: mockSetOptions,
		}),
	};
});

beforeEach(() => {
	mockSetOptions.mockClear();
});

describe('<HelloScreen />', () => {
	it('renders the text "Hello World"', () => {
		const { getByText } = render(
			<Provider store={store}>
				<NavigationContainer theme={DefaultTheme}>
					<HelloScreen />
				</NavigationContainer>
			</Provider>,
		);
		expect(getByText('dev_hello.defaultMessage')).toBeTruthy();
	});

	it('calls Alert.alert when the header button is pressed', async () => {
		render(
			<Provider store={store}>
				<NavigationContainer theme={DefaultTheme}>
					<HelloScreen />
				</NavigationContainer>
			</Provider>,
		);

		await waitFor(() => expect(mockSetOptions).toHaveBeenCalled());

		const headerConfig = mockSetOptions.mock.calls.at(-1)[0];
		const HeaderRightButton = headerConfig.headerRight;

		const { getByText } = render(<HeaderRightButton />);
		fireEvent.press(getByText('dev_hello.buttonAlert'));

		expect(Alert.alert).toHaveBeenCalledWith('dev_hello.buttonAlert');
	});
});
