/**
 * Simple Okta Demo Screen
 *
 * A basic demo screen that showcases the simple Okta login flow.
 * This is a standalone demo following Expo's official documentation pattern.
 *
 * DEV-ONLY; Okta Login PoC; screen to test/show functionality
 */

import React from 'react';
import { StyleSheet, View, ScrollView, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import OktaLogin from '@/components/auth/OktaLogin';

export default function SimpleOktaDemoScreen() {
	return (
		<SafeAreaView style={styles.container}>
			<ScrollView contentContainerStyle={styles.scrollContent}>
				<View style={styles.header}>
					<Text style={styles.headerTitle}>Simple Okta Demo</Text>
					<Text style={styles.headerSubtitle}>
						Testing basic Okta authentication with Expo
					</Text>
				</View>

				<View style={styles.demoSection}>
					<OktaLogin />
				</View>
			</ScrollView>
		</SafeAreaView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#F9FAFB',
	},
	scrollContent: {
		padding: 20,
	},
	header: {
		alignItems: 'center',
		marginBottom: 32,
	},
	headerTitle: {
		fontSize: 28,
		fontWeight: 'bold',
		color: '#111827',
		marginBottom: 8,
	},
	headerSubtitle: {
		fontSize: 16,
		color: '#6B7280',
		textAlign: 'center',
	},
	demoSection: {
		marginBottom: 32,
	},
	infoSection: {
		backgroundColor: '#FFFFFF',
		padding: 20,
		borderRadius: 12,
		marginBottom: 24,
		borderWidth: 1,
		borderColor: '#E5E7EB',
	},
	infoTitle: {
		fontSize: 18,
		fontWeight: '600',
		color: '#374151',
		marginBottom: 12,
	},
	infoText: {
		fontSize: 14,
		color: '#6B7280',
		lineHeight: 20,
		marginBottom: 8,
	},
	bulletPoint: {
		fontSize: 14,
		color: '#6B7280',
		lineHeight: 20,
		marginLeft: 8,
		marginBottom: 4,
	},
	notesSection: {
		backgroundColor: '#FEF3C7',
		padding: 20,
		borderRadius: 12,
		borderWidth: 1,
		borderColor: '#FCD34D',
	},
	notesTitle: {
		fontSize: 16,
		fontWeight: '600',
		color: '#92400E',
		marginBottom: 12,
	},
	notesText: {
		fontSize: 14,
		color: '#92400E',
		lineHeight: 20,
		marginBottom: 8,
	},
});
