/**
 * oktaTokenExchange utils tests
 * DEV PoC Only
 */

import {
	exchangeCodeForTokens,
	refreshAccessToken,
	parseIdToken,
	extractUserProfile,
	generatePKCEParams,
	generateOAuthState,
} from '../oktaTokenExchange';
import { OktaAuthenticationError, OktaAuthError, type OktaIdTokenClaims } from '../../types/okta';

// Local polyfills to keep this as a single-file change
beforeAll(() => {
	// atob/btoa
	if (typeof (global as any).btoa === 'undefined') {
		(global as any).btoa = (str: string) => Buffer.from(str, 'binary').toString('base64');
	}
	if (typeof (global as any).atob === 'undefined') {
		(global as any).atob = (b64: string) => Buffer.from(b64, 'base64').toString('binary');
	}
	// TextEncoder
	if (typeof (global as any).TextEncoder === 'undefined') {
		const { TextEncoder } = require('util');
		(global as any).TextEncoder = TextEncoder;
	}
});

afterEach(() => {
	jest.clearAllMocks();
	jest.useRealTimers();
});

describe('exchangeCodeForTokens', () => {
	const issuer = 'https://test.okta.com/oauth2/default/';
	const baseIssuer = 'https://test.okta.com/oauth2/default';

	it('exchanges authorization code successfully', async () => {
		const mockResponse = {
			access_token: 'access',
			token_type: 'Bearer',
			expires_in: 3600,
			refresh_token: 'refresh',
			id_token: 'id.token',
			scope: 'openid profile email',
		};

		(global as any).fetch = jest.fn().mockResolvedValue({
			ok: true,
			status: 200,
			json: async () => mockResponse,
		});

		const result = await exchangeCodeForTokens(issuer, {
			grantType: 'authorization_code',
			clientId: 'client123',
			code: 'auth-code',
			redirectUri: 'app://callback',
			codeVerifier: 'verifier',
		});

		expect(result).toEqual({
			accessToken: 'access',
			tokenType: 'Bearer',
			expiresIn: 3600,
			refreshToken: 'refresh',
			idToken: 'id.token',
			scope: 'openid profile email',
		});
		expect(global.fetch).toHaveBeenCalledWith(
			`${baseIssuer}/v1/token`,
			expect.objectContaining({ method: 'POST' }),
		);
	});

	it('maps error responses to OktaAuthenticationError', async () => {
		const errorBody = {
			error: 'invalid_grant',
			error_description: 'Bad code',
		};
		(global as any).fetch = jest.fn().mockResolvedValue({
			ok: false,
			status: 400,
			json: async () => errorBody,
		});

		try {
			await exchangeCodeForTokens(baseIssuer, {
				grantType: 'authorization_code',
				clientId: 'client123',
				code: 'bad-code',
				redirectUri: 'app://callback',
			});
			fail('Expected to throw');
		} catch (e) {
			expect(e).toBeInstanceOf(OktaAuthenticationError);
			const err = e as OktaAuthenticationError;
			expect(err.errorType).toBe(OktaAuthError.INVALID_GRANT);
			expect(err.message).toBe('Bad code');
		}
	});

	it('wraps network errors as NETWORK_ERROR', async () => {
		(global as any).fetch = jest.fn().mockRejectedValue(new Error('Network down'));

		try {
			await exchangeCodeForTokens(baseIssuer, {
				grantType: 'refresh_token',
				clientId: 'client123',
				refreshToken: 'r1',
			});
			fail('Expected to throw');
		} catch (e) {
			expect(e).toBeInstanceOf(OktaAuthenticationError);
			const err = e as OktaAuthenticationError;
			expect(err.errorType).toBe(OktaAuthError.NETWORK_ERROR);
		}
	});

	it('handles aborted/timeout as NETWORK_ERROR (simulated)', async () => {
		// Simulate immediate abort rejection
		(global as any).fetch = jest.fn().mockImplementation(() => {
			return Promise.reject(Object.assign(new Error('Aborted'), { name: 'AbortError' }));
		});

		try {
			await exchangeCodeForTokens(baseIssuer, {
				grantType: 'refresh_token',
				clientId: 'client123',
				refreshToken: 'r1',
			});
			fail('Expected to throw');
		} catch (e) {
			expect(e).toBeInstanceOf(OktaAuthenticationError);
			const err = e as OktaAuthenticationError;
			expect(err.errorType).toBe(OktaAuthError.NETWORK_ERROR);
		}
	});
});

describe('refreshAccessToken', () => {
	it('delegates to exchangeCodeForTokens with refresh_token', async () => {
		const body = { access_token: 'a', token_type: 'Bearer', expires_in: 3600 };
		(global as any).fetch = jest
			.fn()
			.mockResolvedValue({ ok: true, status: 200, json: async () => body });

		const res = await refreshAccessToken('https://issuer', 'cid', 'rtok');
		expect(res.accessToken).toBe('a');
	});
});

describe('parseIdToken', () => {
	const base64url = (obj: unknown) =>
		Buffer.from(JSON.stringify(obj))
			.toString('base64')
			.replace(/\+/g, '-')
			.replace(/\//g, '_')
			.replace(/=+$/, '');

	it('parses a valid JWT and validates required claims', () => {
		const now = Math.floor(Date.now() / 1000);
		const claims: Partial<OktaIdTokenClaims> = {
			sub: 'user',
			iss: 'https://issuer',
			aud: 'client',
			iat: now,
			exp: now + 3600,
			email: '<EMAIL>',
		};
		const jwt = `${base64url({ alg: 'none' })}.${base64url(claims)}.sig`;
		const parsed = parseIdToken(jwt);
		expect(parsed.sub).toBe('user');
		const profile = extractUserProfile(parsed);
		expect(profile.email).toBe('<EMAIL>');
	});

	it('throws on invalid JWT format', () => {
		expect(() => parseIdToken('abc.def')).toThrow(OktaAuthenticationError);
	});

	it('throws on expired token', () => {
		const now = Math.floor(Date.now() / 1000);
		const claims = { sub: 'u', iss: 'i', aud: 'c', iat: now - 7200, exp: now - 3600 };
		const jwt = `${base64url({ alg: 'none' })}.${base64url(claims)}.sig`;
		expect(() => parseIdToken(jwt)).toThrow(OktaAuthenticationError);
	});

	it('throws on invalid base64url payload', () => {
		const jwt = `aaa.b@d_payload.sig`;
		expect(() => parseIdToken(jwt)).toThrow(OktaAuthenticationError);
	});
});

describe('PKCE and state helpers', () => {
	it('generatePKCEParams returns verifier/challenge/method', () => {
		const pkce = generatePKCEParams();
		expect(pkce.codeVerifier.length).toBeGreaterThanOrEqual(43);
		expect(pkce.codeVerifier.length).toBeLessThanOrEqual(128);
		expect(pkce.codeChallenge.length).toBeGreaterThan(0);
		expect(pkce.codeChallengeMethod).toBe('S256');
		// URL-safe
		expect(/^[A-Za-z0-9\-_.~]+$/.test(pkce.codeVerifier)).toBe(true);
		expect(/^[A-Za-z0-9\-_]+$/.test(pkce.codeChallenge)).toBe(true);
	});

	it('generateOAuthState returns a random 32-length string', () => {
		const s = generateOAuthState();
		expect(s).toHaveLength(32);
	});
});
