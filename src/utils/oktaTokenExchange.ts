/**
 * Okta Token Exchange Utilities
 *
 * DEV PoC ONLY -- THIS WILL BE MOVED TO MIDDLEWARE
 *
 * Handles OAuth 2.0 token exchange operations including:
 * - Authorization code to token exchange
 * - Token refresh
 * - PKCE (Proof Key for Code Exchange) operations
 * - ID token parsing
 */

import {
	OktaAuthenticationError,
	OktaAuthError,
	type OktaTokenRequest,
	type OktaTokenResponse,
	type OktaIdTokenClaims,
	type OktaUserProfile,
	type PKCEParams,
} from '@/types/okta';

/**
 * Generate PKCE parameters for secure OAuth flow
 */
export function generatePKCEParams(): PKCEParams {
	// Generate a random code verifier (43-128 characters)
	const codeVerifier = generateRandomString(128);

	// Create code challenge using SHA256
	const codeChallenge = base64URLEncode(sha256(codeVerifier));

	return {
		codeVerifier,
		codeChallenge,
		codeChallengeMethod: 'S256',
	};
}

/**
 * Generate a cryptographically secure random string
 */
function generateRandomString(length: number): string {
	const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
	let result = '';

	// Use crypto.getRandomValues if available (web), otherwise use Math.random
	if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
		const array = new Uint8Array(length);
		crypto.getRandomValues(array);
		for (let i = 0; i < length; i++) {
			result += charset[array[i] % charset.length];
		}
	} else {
		// Fallback for React Native
		for (let i = 0; i < length; i++) {
			result += charset[Math.floor(Math.random() * charset.length)];
		}
	}

	return result;
}

/**
 * SHA-256 hash (synchronous, pure JS)
 * Returns an ArrayBuffer-like (Uint8Array.buffer) for base64url encoding.
 * This avoids adding dependencies and works consistently across RN/Web.
 */
function sha256(plain: string): ArrayBufferLike {
	const utf8 = new TextEncoder().encode(plain);

	// SHA-256 constants
	const K = new Uint32Array([
		0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4,
		0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe,
		0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f,
		0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7,
		0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc,
		0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b,
		0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116,
		0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
		0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7,
		0xc67178f2,
	]);

	// Initial hash values
	let h0 = 0x6a09e667;
	let h1 = 0xbb67ae85;
	let h2 = 0x3c6ef372;
	let h3 = 0xa54ff53a;
	let h4 = 0x510e527f;
	let h5 = 0x9b05688c;
	let h6 = 0x1f83d9ab;
	let h7 = 0x5be0cd19;

	// Pre-processing (padding)
	const l = utf8.length * 8; // length in bits
	const withOne = new Uint8Array((utf8.length + 9 + 63) & ~63);
	withOne.set(utf8);
	withOne[utf8.length] = 0x80;
	// Append length (big-endian)
	const lenPos = withOne.length - 8;
	const dv = new DataView(withOne.buffer);
	dv.setUint32(lenPos, Math.floor(l / 0x100000000));
	dv.setUint32(lenPos + 4, l >>> 0);

	const w = new Uint32Array(64);
	for (let i = 0; i < withOne.length; i += 64) {
		// Prepare message schedule
		for (let t = 0; t < 16; t++) {
			const j = i + t * 4;
			w[t] =
				(withOne[j] << 24) |
				(withOne[j + 1] << 16) |
				(withOne[j + 2] << 8) |
				withOne[j + 3];
		}
		for (let t = 16; t < 64; t++) {
			const s0 = rotr(w[t - 15], 7) ^ rotr(w[t - 15], 18) ^ (w[t - 15] >>> 3);
			const s1 = rotr(w[t - 2], 17) ^ rotr(w[t - 2], 19) ^ (w[t - 2] >>> 10);
			w[t] = (w[t - 16] + s0 + w[t - 7] + s1) >>> 0;
		}

		// Initialize working variables
		let a = h0;
		let b = h1;
		let c = h2;
		let d = h3;
		let e = h4;
		let f = h5;
		let g = h6;
		let h = h7;

		// Main compression function
		for (let t = 0; t < 64; t++) {
			const S1 = rotr(e, 6) ^ rotr(e, 11) ^ rotr(e, 25);
			const ch = (e & f) ^ (~e & g);
			const temp1 = (h + S1 + ch + K[t] + w[t]) >>> 0;
			const S0 = rotr(a, 2) ^ rotr(a, 13) ^ rotr(a, 22);
			const maj = (a & b) ^ (a & c) ^ (b & c);
			const temp2 = (S0 + maj) >>> 0;

			h = g;
			g = f;
			f = e;
			e = (d + temp1) >>> 0;
			d = c;
			c = b;
			b = a;
			a = (temp1 + temp2) >>> 0;
		}

		// Add the compressed chunk to the current hash value
		h0 = (h0 + a) >>> 0;
		h1 = (h1 + b) >>> 0;
		h2 = (h2 + c) >>> 0;
		h3 = (h3 + d) >>> 0;
		h4 = (h4 + e) >>> 0;
		h5 = (h5 + f) >>> 0;
		h6 = (h6 + g) >>> 0;
		h7 = (h7 + h) >>> 0;
	}

	// Produce the final hash as bytes
	const out = new Uint8Array(32);
	const outDv = new DataView(out.buffer);
	outDv.setUint32(0, h0);
	outDv.setUint32(4, h1);
	outDv.setUint32(8, h2);
	outDv.setUint32(12, h3);
	outDv.setUint32(16, h4);
	outDv.setUint32(20, h5);
	outDv.setUint32(24, h6);
	outDv.setUint32(28, h7);
	return out.buffer;

	function rotr(x: number, n: number) {
		return (x >>> n) | (x << (32 - n));
	}
}

/**
 * Base64 URL encode
 */
function base64URLEncode(buffer: ArrayBufferLike): string {
	const bytes = new Uint8Array(buffer);
	let binary = '';
	for (let i = 0; i < bytes.byteLength; i++) {
		binary += String.fromCharCode(bytes[i]);
	}

	return btoa(binary).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Exchange authorization code for tokens
 */
export async function exchangeCodeForTokens(
	issuer: string,
	tokenRequest: OktaTokenRequest,
): Promise<OktaTokenResponse> {
	// Normalize issuer and compose token endpoint safely
	const baseIssuer = issuer.replace(/\/+$/, '');
	const tokenEndpoint = `${baseIssuer}/v1/token`;

	// Prepare form data
	const formData = new URLSearchParams();
	formData.append('grant_type', tokenRequest.grantType);
	formData.append('client_id', tokenRequest.clientId);

	if (tokenRequest.grantType === 'authorization_code') {
		if (!tokenRequest.code || !tokenRequest.redirectUri) {
			throw new OktaAuthenticationError(
				OktaAuthError.INVALID_REQUEST,
				'Authorization code and redirect URI are required',
			);
		}
		formData.append('code', tokenRequest.code);
		formData.append('redirect_uri', tokenRequest.redirectUri);

		if (tokenRequest.codeVerifier) {
			formData.append('code_verifier', tokenRequest.codeVerifier);
		}
	} else if (tokenRequest.grantType === 'refresh_token') {
		if (!tokenRequest.refreshToken) {
			throw new OktaAuthenticationError(
				OktaAuthError.INVALID_REQUEST,
				'Refresh token is required',
			);
		}
		formData.append('refresh_token', tokenRequest.refreshToken);
	}

	// Prepare abort controller and timeout outside try so we can always clear it in finally
	const controller = new AbortController();
	let timeoutId: ReturnType<typeof setTimeout> | undefined;

	try {
		if (__DEV__) {
			console.log('🔄 Exchanging code for tokens', {
				endpoint: tokenEndpoint,
				grantType: tokenRequest.grantType,
			});
		}

		// Add a timeout to avoid hanging requests
		timeoutId = setTimeout(() => controller.abort(), 15000);

		const response = await fetch(tokenEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
				Accept: 'application/json',
			},
			body: formData.toString(),
			signal: controller.signal,
		});

		const responseData = await response.json();

		if (!response.ok) {
			if (__DEV__) {
				console.error('❌ Token exchange failed', {
					status: response.status,
					error: responseData?.error,
					error_description: responseData?.error_description,
				});
			}
			throw new OktaAuthenticationError(
				mapOktaErrorToType(responseData.error),
				responseData.error_description || 'Token exchange failed',
				new Error(`HTTP ${response.status}: ${responseData.error}`),
			);
		}

		if (__DEV__) {
			console.log('✅ Token exchange successful');
		}

		return {
			accessToken: responseData.access_token,
			tokenType: responseData.token_type,
			expiresIn: responseData.expires_in,
			refreshToken: responseData.refresh_token,
			idToken: responseData.id_token,
			scope: responseData.scope,
		};
	} catch (error) {
		if (error instanceof OktaAuthenticationError) {
			throw error;
		}

		if (__DEV__) {
			console.error('❌ Network error during token exchange:', error);
		}
		throw new OktaAuthenticationError(
			OktaAuthError.NETWORK_ERROR,
			'Failed to exchange code for tokens',
			error as Error,
		);
	} finally {
		if (timeoutId) clearTimeout(timeoutId);
	}
}

/**
 * Refresh access token using refresh token
 */
export async function refreshAccessToken(
	issuer: string,
	clientId: string,
	refreshToken: string,
): Promise<OktaTokenResponse> {
	return exchangeCodeForTokens(issuer, {
		grantType: 'refresh_token',
		clientId,
		refreshToken,
	});
}

/**
 * Parse and validate ID token (JWT)
 */
export function parseIdToken(idToken: string): OktaIdTokenClaims {
	try {
		// Split JWT into parts
		const parts = idToken.split('.');
		if (parts.length !== 3) {
			throw new Error('Invalid JWT format');
		}

		// Decode payload (base64url)
		const payload = parts[1];
		const decodedPayload = base64URLDecode(payload);
		const claims = JSON.parse(decodedPayload);

		// Validate required claims
		if (!claims.sub || !claims.iss || !claims.aud || !claims.exp || !claims.iat) {
			throw new Error('Missing required JWT claims');
		}

		// Check if token is expired
		const now = Math.floor(Date.now() / 1000);
		if (claims.exp < now) {
			throw new Error('ID token is expired');
		}

		return claims as OktaIdTokenClaims;
	} catch (error) {
		if (__DEV__) {
			console.error('❌ Failed to parse ID token:', error);
		}
		throw new OktaAuthenticationError(
			OktaAuthError.INVALID_GRANT,
			'Failed to parse ID token',
			error as Error,
		);
	}
}

/**
 * Extract user profile from ID token claims
 */
export function extractUserProfile(claims: OktaIdTokenClaims): OktaUserProfile {
	return {
		sub: claims.sub,
		email: claims.email,
		emailVerified: claims.emailVerified,
		name: claims.name,
		preferredUsername: claims.preferredUsername,
		givenName: claims.givenName,
		familyName: claims.familyName,
		locale: claims.locale,
		zoneinfo: claims.zoneinfo,
		groups: claims.groups,
	};
}

/**
 * Base64 URL decode
 */
function base64URLDecode(str: string): string {
	// Add padding if needed
	let padded = str;
	while (padded.length % 4) {
		padded += '=';
	}

	// Replace URL-safe characters
	const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');

	try {
		return atob(base64);
	} catch {
		throw new Error('Invalid base64url encoding');
	}
}

/**
 * Map Okta error codes to our error types
 */
function mapOktaErrorToType(errorCode: string): OktaAuthError {
	switch (errorCode) {
		case 'invalid_request':
			return OktaAuthError.INVALID_REQUEST;
		case 'invalid_client':
			return OktaAuthError.INVALID_CLIENT;
		case 'invalid_grant':
			return OktaAuthError.INVALID_GRANT;
		case 'unauthorized_client':
			return OktaAuthError.UNAUTHORIZED_CLIENT;
		case 'unsupported_grant_type':
			return OktaAuthError.UNSUPPORTED_GRANT_TYPE;
		case 'invalid_scope':
			return OktaAuthError.INVALID_SCOPE;
		default:
			return OktaAuthError.UNKNOWN_ERROR;
	}
}

/**
 * Generate OAuth state parameter
 */
export function generateOAuthState(): string {
	return generateRandomString(32);
}
