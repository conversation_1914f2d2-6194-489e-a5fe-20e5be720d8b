/**
 * Okta Authentication Types (POC ONLY)
 *
 * TypeScript interfaces and types for Okta OAuth 2.0 authentication flow
 *
 * ⚠️  POC IMPLEMENTATION - SEPARATE FROM UNA TYPES
 * - These types are completely separate from UNA user types (src/types/user.ts)
 * - OktaUserProfile is different from UnaUserProfile
 * - No overlap or conflicts with main app user types
 * - For POC/testing purposes only
 */

/**
 * Okta OAuth configuration
 */
export interface OktaConfig {
	/** Okta domain/issuer URL */
	issuer: string;
	/** OAuth client ID */
	clientId: string;
	/** OAuth redirect URI */
	redirectUri: string;
	/** OAuth scopes to request */
	scopes: string[];
	/** Additional OAuth parameters */
	additionalParameters?: Record<string, string>;
}

/**
 * OAuth authorization request parameters
 */
export interface OktaAuthRequest {
	/** OAuth client ID */
	clientId: string;
	/** OAuth redirect URI */
	redirectUri: string;
	/** OAuth response type (typically 'code') */
	responseType: 'code';
	/** OAuth scopes */
	scopes: string[];
	/** OAuth state parameter for CSRF protection */
	state: string;
	/** PKCE code challenge */
	codeChallenge?: string;
	/** PKCE code challenge method */
	codeChallengeMethod?: 'S256';
}

/**
 * OAuth authorization response from callback
 */
export interface OktaAuthResponse {
	/** Response type indicator */
	type: 'success' | 'error' | 'cancel';
	/** Authorization code (on success) */
	code?: string;
	/** OAuth state parameter */
	state?: string;
	/** Error description (on error) */
	error?: string;
	/** Error description (on error) */
	errorDescription?: string;
	/** Additional parameters */
	params?: Record<string, string>;
}

/**
 * Token exchange request to Okta's token endpoint
 */
export interface OktaTokenRequest {
	/** Grant type (authorization_code or refresh_token) */
	grantType: 'authorization_code' | 'refresh_token';
	/** OAuth client ID */
	clientId: string;
	/** Authorization code (for authorization_code grant) */
	code?: string;
	/** Refresh token (for refresh_token grant) */
	refreshToken?: string;
	/** OAuth redirect URI */
	redirectUri?: string;
	/** PKCE code verifier */
	codeVerifier?: string;
}

/**
 * Token response from Okta's token endpoint
 */
export interface OktaTokenResponse {
	/** Access token */
	accessToken: string;
	/** Token type (typically 'Bearer') */
	tokenType: string;
	/** Token expiration in seconds */
	expiresIn: number;
	/** Refresh token */
	refreshToken?: string;
	/** ID token (JWT) */
	idToken?: string;
	/** OAuth scopes granted */
	scope?: string;
}

/**
 * Parsed ID token claims
 */
export interface OktaIdTokenClaims {
	/** Subject (user ID) */
	sub: string;
	/** Issuer */
	iss: string;
	/** Audience */
	aud: string;
	/** Expiration time */
	exp: number;
	/** Issued at time */
	iat: number;
	/** Authentication time */
	authTime?: number;
	/** Nonce */
	nonce?: string;
	/** Email address */
	email?: string;
	/** Email verified flag */
	emailVerified?: boolean;
	/** Full name */
	name?: string;
	/** Preferred username */
	preferredUsername?: string;
	/** Given name */
	givenName?: string;
	/** Family name */
	familyName?: string;
	/** Locale */
	locale?: string;
	/** Zone info */
	zoneinfo?: string;
	/** Groups */
	groups?: string[];
}

/**
 * User profile information
 */
export interface OktaUserProfile {
	/** User ID */
	sub: string;
	/** Email address */
	email?: string;
	/** Email verified status */
	emailVerified?: boolean;
	/** Full name */
	name?: string;
	/** Preferred username */
	preferredUsername?: string;
	/** Given name */
	givenName?: string;
	/** Family name */
	familyName?: string;
	/** Locale */
	locale?: string;
	/** Time zone */
	zoneinfo?: string;
	/** User groups */
	groups?: string[];
}

/**
 * Complete Okta authentication result
 */
export interface OktaAuthResult {
	/** Authentication success status */
	success: boolean;
	/** Access token */
	accessToken?: string;
	/** Refresh token */
	refreshToken?: string;
	/** ID token */
	idToken?: string;
	/** Token type */
	tokenType?: string;
	/** Token expiration timestamp */
	expiresAt?: number;
	/** OAuth scopes */
	scope?: string;
	/** User profile */
	userProfile?: OktaUserProfile;
	/** Error message (if failed) */
	error?: string;
	/** Error details */
	errorDescription?: string;
}

/**
 * Authentication state
 */
export type AuthenticationState =
	| 'unauthenticated'
	| 'authenticating'
	| 'authenticated'
	| 'token_expired'
	| 'refreshing_token'
	| 'error';

/**
 * Authentication error types
 */
export enum OktaAuthError {
	NETWORK_ERROR = 'NETWORK_ERROR',
	INVALID_REQUEST = 'INVALID_REQUEST',
	INVALID_CLIENT = 'INVALID_CLIENT',
	INVALID_GRANT = 'INVALID_GRANT',
	UNAUTHORIZED_CLIENT = 'UNAUTHORIZED_CLIENT',
	UNSUPPORTED_GRANT_TYPE = 'UNSUPPORTED_GRANT_TYPE',
	INVALID_SCOPE = 'INVALID_SCOPE',
	TOKEN_EXPIRED = 'TOKEN_EXPIRED',
	REFRESH_TOKEN_EXPIRED = 'REFRESH_TOKEN_EXPIRED',
	USER_CANCELLED = 'USER_CANCELLED',
	BROWSER_ERROR = 'BROWSER_ERROR',
	UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Custom error class for Okta authentication errors
 */
export class OktaAuthenticationError extends Error {
	constructor(
		public readonly errorType: OktaAuthError,
		message: string,
		public readonly originalError?: Error,
	) {
		super(message);
		this.name = 'OktaAuthenticationError';
	}
}

/**
 * PKCE (Proof Key for Code Exchange) parameters
 */
export interface PKCEParams {
	/** Code verifier */
	codeVerifier: string;
	/** Code challenge */
	codeChallenge: string;
	/** Code challenge method */
	codeChallengeMethod: 'S256';
}
