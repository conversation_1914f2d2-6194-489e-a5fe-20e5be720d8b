/**
 * Okta Authentication Component
 *
 * Simple, reliable OAuth integration that works across web, iOS, and Android.
 *
 * DEV PoC ONLY !!!
 */

import { useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet, Platform } from 'react-native';
import { useAuthRequest, useAutoDiscovery, makeRedirectUri, ResponseType } from 'expo-auth-session';
import { maybeCompleteAuthSession } from 'expo-web-browser';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useOktaAuth } from '@/hooks/useOktaAuth';

// Complete any pending auth session on module load; required for callback redirects to close the webview correctly on all platforms
maybeCompleteAuthSession();

interface OktaLoginProps {
	onAuthSuccess?: (result: any) => void;
	onAuthError?: (error: string) => void;
}

export default function OktaLogin({ onAuthSuccess, onAuthError }: OktaLoginProps) {
	const navigation = useNavigation();
	const {
		authState,
		isLoading,
		isAuthenticated,
		userProfile,
		error,
		handleAuthenticationTokens,
		logout,
		clearError,
		checkAuthStatus,
	} = useOktaAuth();

	// Okta configuration - using environment variables
	const oktaDomain =
		process.env.EXPO_PUBLIC_OKTA_ISSUER;
	const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID;

	// Auto-discovery endpoint as recommended by Expo docs
	const discovery = useAutoDiscovery(oktaDomain);

	// Team-friendly redirect URI approach
	const authRedirectUri = makeRedirectUri({
		scheme: 'learningcoachcommunity',
		path: 'callback',
	});

	if (__DEV__) {
		console.log('🔐 [Auth] Configuration:', {
			oktaDomain,
			clientId: clientId.substring(0, 8) + '...',
			platform: Platform.OS,
			redirectUri: authRedirectUri,
		});
	}

	const [request, response, promptAsync] = useAuthRequest(
		{
			clientId: clientId,
			scopes: ['openid', 'profile', 'email'],
			redirectUri: authRedirectUri,
			responseType: ResponseType.Code,
		},
		discovery,
	);

	// Debug the response object (dev only, without sensitive params)
	if (__DEV__) {
		console.log('🔍 [Auth] Response type:', response?.type);
	}

	// Ensure auth state is refreshed when this screen regains focus (important on Android)
	useFocusEffect(
		useCallback(() => {
			if (__DEV__) {
				console.log('🔁 [Auth] Screen focused; checking auth status');
			}
			checkAuthStatus();
		}, [checkAuthStatus]),
	);

	// Handle authentication response from useAuthRequest (for iOS and web)
	useEffect(() => {
		if (!response) return;

		if (__DEV__) {
			console.log('🔍 [Auth] useEffect triggered with response:', {
				type: response?.type,
				hasParams: !!(response as any)?.params,
				hasAuthentication: !!(response as any)?.authentication,
				platform: Platform.OS,
				timestamp: new Date().toISOString(),
			});
		}

		const handleResponse = async () => {
			if (response.type === 'success') {
				if (__DEV__) {
					console.log('✅ Authentication success (useAuthRequest)');
				}

				// Check if we have tokens from automatic exchange
				if ((response as any).authentication) {
					if (__DEV__) {
						console.log('🎯 [Auth] Tokens received from automatic exchange');
					}
					// Process the pre-exchanged tokens directly
					handleAuthenticationTokens((response as any).authentication);
				} else if ((response as any).params?.code) {
					if (__DEV__) {
						console.log('🔄 [Auth] Authorization code received; exchanging for tokens');
					}
					const authCode = (response as any).params.code as string;
					const state = (response as any).params.state as string | undefined;

					// Validate state to prevent CSRF
					const expectedState = request?.state;
					if (expectedState && state !== expectedState) {
						onAuthError?.('Security check failed (state mismatch). Please try again.');
						if (__DEV__) {
							console.warn('⚠️ [Auth] State mismatch', {
								expected: expectedState,
								received: state,
							});
						}
						return;
					}

					// Import the token exchange utility directly
					const { exchangeCodeForTokens } = await import('@/utils/oktaTokenExchange');

					try {
						// Make the token exchange with PKCE verifier
						const tokenResponse = await exchangeCodeForTokens(oktaDomain, {
							grantType: 'authorization_code',
							clientId: clientId,
							code: authCode,
							redirectUri: authRedirectUri,
							codeVerifier: request?.codeVerifier, // Include PKCE verifier
						});

						if (__DEV__) {
							console.log('🎯 [Auth] Tokens received successfully');
						}

						// Create authentication object for the hook
						const authData = {
							accessToken: tokenResponse.accessToken,
							tokenType: tokenResponse.tokenType,
							expiresIn: tokenResponse.expiresIn,
							refreshToken: tokenResponse.refreshToken,
							idToken: tokenResponse.idToken,
							scope: tokenResponse.scope,
						};

						handleAuthenticationTokens(authData);
					} catch (tokenError) {
						if (__DEV__) {
							console.error('❌ [Auth] Token exchange failed');
						}
						onAuthError?.(`Token exchange failed: ${tokenError}`);
					}
				} else {
					if (__DEV__) {
						console.error('❌ [Auth] No tokens or code in successful response');
					}
					onAuthError?.('Authentication succeeded but no tokens received');
				}
			} else if (response.type === 'error') {
				if (__DEV__) {
					console.error('❌ Authentication Error (useAuthRequest)');
				}
				const errorMessage = response.error?.message || 'Authentication failed';
				onAuthError?.(errorMessage);
			} else if (response.type === 'cancel') {
				if (__DEV__) {
					console.log('🚫 Authentication Cancelled (useAuthRequest)');
				}
				onAuthError?.('Authentication cancelled');
			}
		};

		handleResponse();
	}, [
		response,
		handleAuthenticationTokens,
		onAuthSuccess,
		onAuthError,
		clientId,
		authRedirectUri,
		discovery,
		request,
		oktaDomain,
	]);

	// Handle authentication state changes
	useEffect(() => {
		if (error) {
			Alert.alert('Authentication Error', error, [{ text: 'OK', onPress: clearError }]);
			onAuthError?.(error);
		}
	}, [error, onAuthError, clearError]);

	useEffect(() => {
		if (isAuthenticated && userProfile) {
			Alert.alert(
				'Authentication Success!',
				`Welcome, ${userProfile.name || userProfile.email || 'User'}!`,
			);
			onAuthSuccess?.({
				success: true,
				userProfile,
				authState,
			});

			// On Android, the redirect can push a duplicate of the same screen on top of the stack.
			// Mimic the manual back action to reveal the original instance with correct state.
			if (Platform.OS === 'android') {
				setTimeout(() => {
					try {
						// @ts-ignore - navigation type is generic here; canGoBack exists at runtime
						if (navigation?.canGoBack?.()) {
							// @ts-ignore - goBack exists at runtime
							navigation.goBack();
						}
					} catch (e) {
						if (__DEV__) console.warn('⚠️ [Auth] Failed to navigate back after success', e);
					}
				}, 0);
			}
		}
	}, [isAuthenticated, userProfile, authState, onAuthSuccess, navigation]);

	const handleLogin = async () => {
		if (!request) {
			Alert.alert('Error', 'Authentication request not ready. Please wait and try again.');
			return;
		}

		try {
			if (__DEV__) {
				console.log('🔐 Starting authentication with URI:', authRedirectUri);
				console.log('🔐 Full auth request config:', {
					clientId: clientId.substring(0, 8) + '...',
					scopes: ['openid', 'profile', 'email'],
					redirectUri: authRedirectUri,
					platform: Platform.OS,
					discoveryEndpoint: oktaDomain,
				});
			}

			// Start the OAuth flow with expo-auth-session
			// Note: We'll let the callback handler process the result through our service
			const result = await promptAsync();
			if (__DEV__) {
				console.log('🔐 promptAsync result:', { type: result.type });
			}
		} catch (error) {
			if (__DEV__) {
				console.error('🔐 Login error:', error);
			}
			Alert.alert('Login Error', 'Failed to start authentication process');
		}
	};

	const getStatusColor = () => {
		if (isAuthenticated) return '#10B981'; // green
		if (error) return '#EF4444'; // red
		if (authState === 'authenticating' || authState === 'refreshing_token') return '#F59E0B'; // yellow
		return '#6B7280'; // gray
	};

	const getStatusText = () => {
		if (isLoading) return 'Processing...';
		if (isAuthenticated) return 'Authenticated';
		if (error) return `Error: ${error}`;
		if (authState === 'authenticating') return 'Authenticating...';
		if (authState === 'refreshing_token') return 'Refreshing token...';
		return 'Ready to authenticate';
	};

	// Validate configuration and readiness for login
	const isClientIdValid = !!clientId && clientId !== 'your-client-id-here';
	const isDomainValid = /^https?:\/\/.+/i.test(oktaDomain);
	const isDiscoveryReady = !!discovery;
	const isRequestReady = !!request;
	const canLogin =
		isClientIdValid && isDomainValid && isDiscoveryReady && isRequestReady && !isLoading;

	return (
		<View style={styles.container}>
			<Text style={styles.title}>Okta Authentication</Text>
			<Text style={styles.subtitle}>Platform-Aware OAuth Integration</Text>

			{__DEV__ && (
				<View style={styles.configSection}>
					<Text style={styles.configTitle}>Configuration:</Text>
					<Text style={styles.configText}>Domain: {oktaDomain}</Text>
					<Text style={styles.configText}>Client ID: {clientId.substring(0, 8)}...</Text>
					<Text style={styles.configText}>Platform: {Platform.OS}</Text>
					<Text style={styles.configText}>Redirect URI: {authRedirectUri}</Text>
				</View>
			)}

			<View style={styles.statusSection}>
				<View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
				<Text style={[styles.statusText, { color: getStatusColor() }]}>
					{getStatusText()}
				</Text>
			</View>

			<TouchableOpacity
				style={[styles.loginButton, !canLogin && styles.loginButtonDisabled]}
				onPress={handleLogin}
				disabled={!canLogin}
				accessibilityRole='button'
				accessibilityLabel='Login with Okta'
				accessibilityHint='Opens a secure browser window to sign in with Okta'
				accessibilityState={{ disabled: !canLogin, busy: isLoading }}
			>
				<Text style={styles.loginButtonText}>
					{isLoading ? 'Authenticating...' : 'Login with Okta'}
				</Text>
			</TouchableOpacity>

			{isAuthenticated && (
				<TouchableOpacity
					style={[styles.loginButton, { backgroundColor: '#EF4444', marginTop: 8 }]}
					onPress={logout}
					disabled={isLoading}
					accessibilityRole='button'
					accessibilityLabel='Logout and clear authentication state'
					accessibilityHint='Removes tokens and signs you out'
					accessibilityState={{ disabled: isLoading, busy: isLoading }}
				>
					<Text style={styles.loginButtonText}>Logout / Clear State</Text>
				</TouchableOpacity>
			)}

			{isAuthenticated && userProfile && (
				<View style={styles.successSection}>
					<Text style={styles.successTitle}>✅ Success!</Text>
					<Text style={styles.successText}>
						Authentication completed successfully! Tokens have been securely stored.
					</Text>

					<View style={styles.responseSection}>
						<Text style={styles.responseTitle}>👤 User Profile:</Text>
						<Text style={styles.responseText}>
							Name: {userProfile.name || 'Not provided'}
						</Text>
						<Text style={styles.responseText}>
							Email: {userProfile.email || 'Not provided'}
						</Text>
						<Text style={styles.responseText}>
							Username: {userProfile.preferredUsername || 'Not provided'}
						</Text>
						<Text style={styles.responseText}>
							User ID: {userProfile.sub?.substring(0, 20)}...
						</Text>
						<Text style={styles.responseText}>Platform: {Platform.OS}</Text>
						<Text style={styles.responseText}>Auth State: {authState}</Text>
					</View>

					<TouchableOpacity
						style={[styles.loginButton, { backgroundColor: '#EF4444' }]}
						onPress={logout}
						disabled={isLoading}
						accessibilityRole='button'
						accessibilityLabel='Logout'
						accessibilityHint='Signs you out of the application'
						accessibilityState={{ disabled: isLoading, busy: isLoading }}
					>
						<Text style={styles.loginButtonText}>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Text>
					</TouchableOpacity>
				</View>
			)}
		</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 20,
		backgroundColor: '#F9FAFB',
	},
	title: {
		fontSize: 24,
		fontWeight: 'bold',
		textAlign: 'center',
		marginBottom: 8,
		color: '#111827',
	},
	subtitle: {
		fontSize: 16,
		textAlign: 'center',
		marginBottom: 32,
		color: '#6B7280',
	},
	configSection: {
		backgroundColor: '#FFFFFF',
		padding: 16,
		borderRadius: 8,
		marginBottom: 24,
		borderWidth: 1,
		borderColor: '#E5E7EB',
	},
	configTitle: {
		fontSize: 16,
		fontWeight: '600',
		marginBottom: 8,
		color: '#374151',
	},
	configText: {
		fontSize: 14,
		color: '#6B7280',
		marginBottom: 4,
		fontFamily: 'monospace',
	},
	statusSection: {
		flexDirection: 'row',
		alignItems: 'center',
		marginBottom: 24,
		paddingHorizontal: 4,
	},
	statusIndicator: {
		width: 12,
		height: 12,
		borderRadius: 6,
		marginRight: 12,
	},
	statusText: {
		fontSize: 16,
		fontWeight: '500',
	},
	loginButton: {
		backgroundColor: '#3B82F6',
		paddingVertical: 16,
		paddingHorizontal: 32,
		borderRadius: 8,
		alignItems: 'center',
		marginBottom: 24,
	},
	loginButtonDisabled: {
		backgroundColor: '#9CA3AF',
	},
	loginButtonText: {
		color: '#FFFFFF',
		fontSize: 16,
		fontWeight: '600',
	},
	successSection: {
		backgroundColor: '#ECFDF5',
		padding: 16,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: '#10B981',
	},
	successTitle: {
		fontSize: 18,
		fontWeight: '600',
		color: '#065F46',
		marginBottom: 8,
	},
	successText: {
		fontSize: 14,
		color: '#047857',
		lineHeight: 20,
		marginBottom: 16,
	},
	responseSection: {
		backgroundColor: '#F3F4F6',
		padding: 12,
		borderRadius: 6,
		borderWidth: 1,
		borderColor: '#D1D5DB',
	},
	responseTitle: {
		fontSize: 14,
		fontWeight: '600',
		color: '#374151',
		marginBottom: 8,
	},
	responseText: {
		fontSize: 12,
		color: '#6B7280',
		fontFamily: 'monospace',
		marginBottom: 4,
	},
});
