import { useTheme as useNavTheme, Theme as NavTheme } from '@react-navigation/native';
import { fonts } from './fonts';
import { Colors, ColorsProps } from '@/theme/Colors';

export type AppTheme = Omit<NavTheme, 'colors'> & {
	colors: NavTheme['colors'] & ColorsProps;
};

export const DefaultTheme: AppTheme = {
	dark: false,
	colors: Colors.light,
	fonts,
};

export function useTheme(): AppTheme {
	return useNavTheme<AppTheme>();
}
