/**
 * Validation Tests for SecureStorageService
 *
 * Tests the improved validation logic using centralized validators
 */

import { SecureStorageService, ValidationError } from '../secureStorageService';
import { AuthData } from '@/repositories/secureStorageRepository';

// Mock the repository
jest.mock('@/repositories/secureStorageRepository', () => ({
	secureStorageRepository: {
		saveAuthData: jest.fn(),
		getAuthData: jest.fn(),
		clearAuthData: jest.fn(),
		setObject: jest.fn(),
		getObject: jest.fn(),
		removeItem: jest.fn(),
		removeObject: jest.fn(),
		clearAllUserData: jest.fn(),
	},
}));

describe('SecureStorageService Validation', () => {
	let service: SecureStorageService;

	beforeEach(() => {
		service = new SecureStorageService();
		jest.clearAllMocks();
	});

	describe('saveAuthData validation', () => {
		const validAuthData: AuthData = {
			token: 'valid-access-token-12345',
			refreshToken: 'valid-refresh-token-12345',
			expiresAt: Date.now() + 3600000, // 1 hour from now
			userProfile: {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User',
			},
		};

		it('should accept valid auth data', async () => {
			await expect(service.saveAuthData(validAuthData)).resolves.not.toThrow();
		});

		it('should reject missing access token', async () => {
			const invalidData = { ...validAuthData, token: '' };
			await expect(service.saveAuthData(invalidData)).rejects.toThrow(ValidationError);
			await expect(service.saveAuthData(invalidData)).rejects.toThrow('token:');
		});

		it('should reject short access token', async () => {
			const invalidData = { ...validAuthData, token: 'short' };
			await expect(service.saveAuthData(invalidData)).rejects.toThrow(ValidationError);
			await expect(service.saveAuthData(invalidData)).rejects.toThrow('at least 10 characters');
		});

		it('should reject missing refresh token', async () => {
			const invalidData = { ...validAuthData, refreshToken: '' };
			await expect(service.saveAuthData(invalidData)).rejects.toThrow(ValidationError);
			await expect(service.saveAuthData(invalidData)).rejects.toThrow('refreshToken:');
		});

		it('should reject expired token', async () => {
			const invalidData = { ...validAuthData, expiresAt: Date.now() - 1000 };
			await expect(service.saveAuthData(invalidData)).rejects.toThrow(ValidationError);
			await expect(service.saveAuthData(invalidData)).rejects.toThrow('must be in the future');
		});

		it('should reject invalid user profile email', async () => {
			const invalidData = {
				...validAuthData,
				userProfile: { ...validAuthData.userProfile!, email: 'invalid-email' },
			};
			await expect(service.saveAuthData(invalidData)).rejects.toThrow(ValidationError);
			await expect(service.saveAuthData(invalidData)).rejects.toThrow('valid email address');
		});

		it('should reject missing user profile sub', async () => {
			const invalidData = {
				...validAuthData,
				userProfile: { ...validAuthData.userProfile!, sub: '' },
			};
			await expect(service.saveAuthData(invalidData)).rejects.toThrow(ValidationError);
			await expect(service.saveAuthData(invalidData)).rejects.toThrow('userProfile.sub');
		});
	});

	describe('storeSecureData validation', () => {
		it('should accept valid key and data', async () => {
			await expect(service.storeSecureData('valid-key', { data: 'test' })).resolves.not.toThrow();
		});

		it('should reject empty key', async () => {
			await expect(service.storeSecureData('', { data: 'test' })).rejects.toThrow(ValidationError);
			await expect(service.storeSecureData('', { data: 'test' })).rejects.toThrow('key:');
		});

		it('should reject whitespace-only key', async () => {
			await expect(service.storeSecureData('   ', { data: 'test' })).rejects.toThrow(ValidationError);
			await expect(service.storeSecureData('   ', { data: 'test' })).rejects.toThrow('whitespace only');
		});

		it('should reject null data', async () => {
			await expect(service.storeSecureData('valid-key', null)).rejects.toThrow(ValidationError);
			await expect(service.storeSecureData('valid-key', null)).rejects.toThrow('null or undefined');
		});

		it('should reject undefined data', async () => {
			await expect(service.storeSecureData('valid-key', undefined)).rejects.toThrow(ValidationError);
			await expect(service.storeSecureData('valid-key', undefined)).rejects.toThrow('null or undefined');
		});
	});

	describe('updateTokens validation', () => {
		beforeEach(() => {
			// Mock existing auth data
			const mockAuthData: AuthData = {
				token: 'old-token',
				refreshToken: 'old-refresh-token',
				expiresAt: Date.now() + 1800000, // 30 minutes from now
			};
			(service.getAuthData as jest.Mock) = jest.fn().mockResolvedValue(mockAuthData);
		});

		it('should accept valid token update', async () => {
			await expect(
				service.updateTokens('new-access-token-12345', 'new-refresh-token-12345', 3600)
			).resolves.not.toThrow();
		});

		it('should reject empty access token', async () => {
			await expect(service.updateTokens('', 'refresh-token', 3600)).rejects.toThrow(ValidationError);
			await expect(service.updateTokens('', 'refresh-token', 3600)).rejects.toThrow('accessToken:');
		});

		it('should reject short access token', async () => {
			await expect(service.updateTokens('short', 'refresh-token', 3600)).rejects.toThrow(ValidationError);
			await expect(service.updateTokens('short', 'refresh-token', 3600)).rejects.toThrow('at least 10 characters');
		});

		it('should reject invalid refresh token when provided', async () => {
			await expect(service.updateTokens('valid-access-token-12345', 'short', 3600)).rejects.toThrow(ValidationError);
			await expect(service.updateTokens('valid-access-token-12345', 'short', 3600)).rejects.toThrow('at least 10 characters');
		});

		it('should reject negative expires in', async () => {
			await expect(service.updateTokens('valid-access-token-12345', undefined, -100)).rejects.toThrow(ValidationError);
			await expect(service.updateTokens('valid-access-token-12345', undefined, -100)).rejects.toThrow('positive number');
		});

		it('should reject zero expires in', async () => {
			await expect(service.updateTokens('valid-access-token-12345', undefined, 0)).rejects.toThrow(ValidationError);
			await expect(service.updateTokens('valid-access-token-12345', undefined, 0)).rejects.toThrow('positive number');
		});
	});

	describe('storage key validation', () => {
		it('should reject empty key in getSecureData', async () => {
			await expect(service.getSecureData('')).rejects.toThrow(ValidationError);
			await expect(service.getSecureData('')).rejects.toThrow('key:');
		});

		it('should reject empty key in removeSecureData', async () => {
			await expect(service.removeSecureData('')).rejects.toThrow(ValidationError);
			await expect(service.removeSecureData('')).rejects.toThrow('key:');
		});

		it('should reject empty key in removeSecureObject', async () => {
			await expect(service.removeSecureObject('')).rejects.toThrow(ValidationError);
			await expect(service.removeSecureObject('')).rejects.toThrow('key:');
		});
	});
});
