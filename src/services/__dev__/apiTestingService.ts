/**
 * API Testing Service (Development Only)
 *
 * Business logic layer for API testing operations.
 * Handles complex workflows, validation, and orchestration of API tests.
 *
 * AI-generated; for use in Api Keys test tab; can probably be removed
 * This file should NOT be included in production builds.
 */

import { ApiFactory } from '../apiFactory';
import { apiConfigService } from '../apiConfigService';
import { apiConfigTestingService } from './apiConfigTestingService';
import type { ISecureStorageRepository } from '@/types';

export interface ApiTestResult {
	success: boolean;
	status?: number;
	data?: any;
	error?: string;
}

export interface TestSuiteResults {
	configTests: {
		save: boolean | null;
		retrieve: boolean | null;
		exists: boolean | null;
		saveUrl: boolean | null;
		retrieveUrl: boolean | null;
	};
	apiTests: {
		basicCall: boolean | null;
		factoryDemo: boolean | null;
	};
	allPassed: boolean;
	apiResponse?: string;
}

/**
 * Service for API testing business operations (Development Only)
 */
export class ApiTestingService {
	/**
	 * Test API call using factory pattern
	 */
	async testApiCall(apiKey: string, apiBaseUrl: string): Promise<ApiTestResult> {
		if (!apiKey || !apiBaseUrl) {
			return {
				success: false,
				error: 'Both API key and base URL are required',
			};
		}

		try {
			// Create custom storage for factory pattern demonstration
			const customStorage: ISecureStorageRepository = {
				getItem: async (key: string) => {
					if (key === 'auth_data') {
						return apiKey;
					}
					return null;
				},
				setItem: async (_key: string, _value: string) => {},
				removeItem: async (_key: string) => {},
				setObject: async <T>(_key: string, _value: T) => {},
				getObject: async <T>(_key: string) => null,
				removeObject: async (_key: string) => {},
				hasItem: async (_key: string) => false,
			};

			// Demonstrate factory pattern (even though we use direct fetch due to auth scheme)
			const _apiClient = ApiFactory.createApiClient(
				undefined,
				customStorage,
				apiConfigService,
				{ baseUrl: apiBaseUrl, timeout: 10000 },
			);
			void _apiClient; // Acknowledge factory pattern demonstration

			// Make API call (using direct fetch due to auth scheme differences)
			const result = await this.makeDirectApiCall(apiKey, apiBaseUrl);
			return result;
		} catch (error: any) {
			return {
				success: false,
				error: error.message,
			};
		}
	}

	/**
	 * Run comprehensive test suite
	 */
	async runTestSuite(
		apiKey: string | null,
		apiBaseUrl: string | null,
	): Promise<TestSuiteResults> {
		const results: TestSuiteResults = {
			configTests: {
				save: null,
				retrieve: null,
				exists: null,
				saveUrl: null,
				retrieveUrl: null,
			},
			apiTests: {
				basicCall: null,
				factoryDemo: null,
			},
			allPassed: false,
			apiResponse: undefined,
		};

		// Test configuration operations
		await this.runConfigTests(results, apiKey, apiBaseUrl);

		// Test API operations if we have credentials
		if (apiKey && apiBaseUrl) {
			await this.runApiTests(results, apiKey, apiBaseUrl);
		}

		// Determine overall success
		results.allPassed = this.calculateOverallSuccess(results, !!apiBaseUrl);

		return results;
	}

	/**
	 * Test factory pattern demonstration
	 */
	async testFactoryPattern(apiKey: string, apiBaseUrl: string): Promise<ApiTestResult> {
		console.log('🏭 Demonstrating API Factory pattern...');
		console.log('🔧 Factory pattern benefits:');
		console.log('  - Dependency injection for config service');
		console.log('  - Customizable options (baseUrl, timeout, retryAttempts)');
		console.log('  - Consistent interface across different implementations');
		console.log('  - Centralized API client creation');

		return this.testApiCall(apiKey, apiBaseUrl);
	}

	/**
	 * Private helper: Make direct API call
	 */
	private async makeDirectApiCall(apiKey: string, apiBaseUrl: string): Promise<ApiTestResult> {
		const baseUrl = apiBaseUrl.replace(/\/$/, '');
		const testUrl = `${baseUrl}/api.php?r=system/test/TemplServiceLogin&params=[10]`;

		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), 10000);

		try {
			const response = await fetch(testUrl, {
				method: 'GET',
				headers: {
					Authorization: apiKey,
					'Content-Type': 'application/json',
				},
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			if (!response.ok) {
				return {
					success: false,
					status: response.status,
					error: `HTTP ${response.status}: ${response.statusText}`,
				};
			}

			const data = await response.json();
			return {
				success: true,
				status: response.status,
				data: data,
			};
		} catch (error: any) {
			clearTimeout(timeoutId);
			return {
				success: false,
				error: error.message,
			};
		}
	}

	/**
	 * Private helper: Run configuration tests
	 */
	private async runConfigTests(
		results: TestSuiteResults,
		apiKey: string | null,
		apiBaseUrl: string | null,
	): Promise<void> {
		// Test retrieve API key
		try {
			const retrieveKeyResult = await apiConfigTestingService.testRetrieveApiKey();
			results.configTests.retrieve =
				retrieveKeyResult.success && retrieveKeyResult.retrievedKey === apiKey;
		} catch (error) {
			console.error('Retrieve key test failed:', error);
			results.configTests.retrieve = false;
		}

		// Test retrieve API base URL
		try {
			const retrieveUrlResult = await apiConfigTestingService.testRetrieveApiBaseUrl();
			results.configTests.retrieveUrl =
				retrieveUrlResult.success && retrieveUrlResult.retrievedUrl === apiBaseUrl;
		} catch (error) {
			console.error('Retrieve URL test failed:', error);
			results.configTests.retrieveUrl = false;
		}

		// Test exists check
		try {
			const existsResult = await apiConfigTestingService.testApiKeyExists();
			results.configTests.exists = existsResult.success && existsResult.exists;
		} catch (error) {
			console.error('Exists test failed:', error);
			results.configTests.exists = false;
		}

		// Mark save tests as passed if we got here
		results.configTests.save = true;
		results.configTests.saveUrl = true;
	}

	/**
	 * Private helper: Run API tests
	 */
	private async runApiTests(
		results: TestSuiteResults,
		apiKey: string,
		apiBaseUrl: string,
	): Promise<void> {
		try {
			const apiTestResult = await this.testApiCall(apiKey, apiBaseUrl);
			results.apiTests.basicCall = apiTestResult.success;
			results.apiTests.factoryDemo = apiTestResult.success;

			if (apiTestResult.success) {
				results.apiResponse = `Status: ${apiTestResult.status}\n\nResponse:\n${JSON.stringify(apiTestResult.data, null, 2)}`;
			} else {
				results.apiResponse = apiTestResult.status
					? `Status: ${apiTestResult.status}\n\nError: ${apiTestResult.error}`
					: `Error: ${apiTestResult.error}`;
			}
		} catch (error) {
			console.error('API test failed:', error);
			results.apiTests.basicCall = false;
			results.apiTests.factoryDemo = false;
		}
	}

	/**
	 * Private helper: Calculate overall success
	 */
	private calculateOverallSuccess(results: TestSuiteResults, hasApiBaseUrl: boolean): boolean {
		const configSuccess = results.configTests.retrieve && results.configTests.exists;
		const urlSuccess = hasApiBaseUrl ? results.configTests.retrieveUrl : true;
		const apiSuccess = hasApiBaseUrl ? results.apiTests.basicCall : true;

		return !!(configSuccess && urlSuccess && apiSuccess);
	}
}

// Export singleton instance for development use
export const apiTestingService = new ApiTestingService();
