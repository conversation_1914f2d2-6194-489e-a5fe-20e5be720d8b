/**
 * Okta Authentication Service (POC ONLY)
 *
 * Business logic layer for Okta OAuth 2.0 authentication.
 * Handles the complete authentication flow including login, token exchange,
 * token refresh, logout, and authentication state management.
 *
 * ⚠️  POC IMPLEMENTATION - SEPARATE FROM UNA AUTHENTICATION
 * - This service is completely independent from UNA authentication
 * - Uses separate storage keys (okta_*) to avoid conflicts
 * - Does not integrate with main Redux user state
 * - For POC/testing purposes only
 *
 * Production implementation will likely be in middleware layer
 */

import { makeRedirectUri } from 'expo-auth-session';
import { OktaAuthRepository } from '@/repositories/oktaAuthRepository';
import {
	exchangeCodeForTokens,
	refreshAccessToken,
	parseIdToken,
	extractUserProfile,
	generatePKCEParams,
	generateOAuthState,
} from '@/utils/oktaTokenExchange';
import {
	OktaAuthenticationError,
	OktaAuthError,
	type OktaConfig,
	type OktaAuthRequest,
	type OktaAuthResponse,
	type OktaAuthResult,
	type OktaUserProfile,
	type AuthenticationState,
	type PKCEParams,
} from '@/types/okta';

/**
 * Okta Authentication Service
 */
export class OktaAuthService {
	private repository: OktaAuthRepository;
	private config: OktaConfig;
	private currentPKCE: PKCEParams | null = null;

	constructor(repository: OktaAuthRepository, config: OktaConfig) {
		this.repository = repository;
		this.config = config;
	}

	/**
	 * Initialize authentication request
	 * Returns the authorization URL and stores necessary parameters
	 */
	async initializeAuth(): Promise<OktaAuthRequest> {
		try {
			// Generate PKCE parameters for security
			this.currentPKCE = generatePKCEParams();
			await this.repository.savePKCEVerifier(this.currentPKCE.codeVerifier);

			// Generate and store OAuth state
			const state = generateOAuthState();
			await this.repository.saveOAuthState(state);

			// Update authentication state
			await this.repository.saveAuthState('authenticating');

			const authRequest: OktaAuthRequest = {
				clientId: this.config.clientId,
				redirectUri: this.config.redirectUri,
				responseType: 'code',
				scopes: this.config.scopes,
				state,
				codeChallenge: this.currentPKCE.codeChallenge,
				codeChallengeMethod: this.currentPKCE.codeChallengeMethod,
			};

			console.log('🔐 [OktaAuthService] Authentication initialized:', {
				clientId: this.config.clientId.substring(0, 8) + '...',
				redirectUri: this.config.redirectUri,
				scopes: this.config.scopes,
				state: state.substring(0, 8) + '...',
			});

			return authRequest;
		} catch (error) {
			await this.repository.saveAuthState('error');
			throw new OktaAuthenticationError(
				OktaAuthError.UNKNOWN_ERROR,
				'Failed to initialize authentication',
				error as Error,
			);
		}
	}

	/**
	 * Process authentication callback
	 * Exchanges authorization code for tokens and stores the result
	 */
	async processAuthCallback(response: OktaAuthResponse): Promise<OktaAuthResult> {
		try {
			if (response.type !== 'success' || !response.code) {
				await this.repository.saveAuthState('error');

				if (response.type === 'cancel') {
					throw new OktaAuthenticationError(
						OktaAuthError.USER_CANCELLED,
						'User cancelled authentication',
					);
				}

				throw new OktaAuthenticationError(
					OktaAuthError.INVALID_REQUEST,
					response.error || 'Authentication failed',
				);
			}

			// Note: State validation is handled by expo-auth-session
			// We skip our own state validation when using expo-auth-session

			// Note: PKCE is handled by expo-auth-session
			// We don't need to manage PKCE verifiers when using expo-auth-session

			console.log('🔄 [OktaAuthService] Exchanging authorization code for tokens...');

			// Exchange authorization code for tokens
			const tokenResponse = await exchangeCodeForTokens(this.config.issuer, {
				grantType: 'authorization_code',
				clientId: this.config.clientId,
				code: response.code,
				redirectUri: this.config.redirectUri,
				// Note: PKCE is handled by expo-auth-session, so we don't pass codeVerifier
			});

			// Parse user profile from ID token if available
			let userProfile: OktaUserProfile | undefined;
			if (tokenResponse.idToken) {
				try {
					const idTokenClaims = parseIdToken(tokenResponse.idToken);
					userProfile = extractUserProfile(idTokenClaims);
					console.log('✅ [OktaAuthService] User profile extracted from ID token');
				} catch (error) {
					console.warn('⚠️ [OktaAuthService] Failed to parse ID token:', error);
					// Continue without user profile - not critical
				}
			}

			// Create authentication result
			const authResult: OktaAuthResult = {
				success: true,
				accessToken: tokenResponse.accessToken,
				refreshToken: tokenResponse.refreshToken,
				idToken: tokenResponse.idToken,
				tokenType: tokenResponse.tokenType,
				expiresAt: Date.now() + tokenResponse.expiresIn * 1000,
				scope: tokenResponse.scope,
				userProfile,
			};

			// Store authentication data
			await this.repository.saveAuthData(authResult);
			await this.repository.saveAuthState('authenticated');

			console.log('✅ [OktaAuthService] Authentication completed successfully');

			return authResult;
		} catch (error) {
			await this.repository.saveAuthState('error');

			if (error instanceof OktaAuthenticationError) {
				throw error;
			}

			throw new OktaAuthenticationError(
				OktaAuthError.UNKNOWN_ERROR,
				'Failed to process authentication callback',
				error as Error,
			);
		}
	}

	/**
	 * Check if user is currently authenticated
	 */
	async isAuthenticated(): Promise<boolean> {
		try {
			const authData = await this.repository.getAuthData();
			if (!authData || !authData.token) {
				return false;
			}
			return authData.expiresAt > Date.now();
		} catch (error) {
			console.error('❌ [OktaAuthService] Failed to check authentication status:', error);
			return false;
		}
	}

	/**
	 * Get current user profile
	 */
	async getUserProfile(): Promise<OktaUserProfile | null> {
		try {
			return await this.repository.getUserProfile();
		} catch (error) {
			console.error('❌ [OktaAuthService] Failed to get user profile:', error);
			return null;
		}
	}

	/**
	 * Get current access token
	 */
	async getAccessToken(): Promise<string | null> {
		try {
			const authData = await this.repository.getAuthData();
			if (!authData || !authData.token) {
				return null;
			}

			// Refresh if token expires within 5 minutes
			const fiveMinutesFromNow = Date.now() + 5 * 60 * 1000;
			if (authData.expiresAt <= fiveMinutesFromNow) {
				console.log('🔄 [OktaAuthService] Token needs refresh, attempting refresh...');
				const refreshed = await this.refreshTokenIfNeeded();
				if (!refreshed) {
					return null;
				}
			}

			return await this.repository.getAccessToken();
		} catch (error) {
			console.error('❌ [OktaAuthService] Failed to get access token:', error);
			return null;
		}
	}

	/**
	 * Refresh access token if needed
	 */
	async refreshTokenIfNeeded(): Promise<boolean> {
		try {
			const refreshToken = await this.repository.getRefreshToken();
			if (!refreshToken) {
				console.log('❌ [OktaAuthService] No refresh token available');
				await this.repository.saveAuthState('unauthenticated');
				return false;
			}

			await this.repository.saveAuthState('refreshing_token');
			console.log('🔄 [OktaAuthService] Refreshing access token...');

			const tokenResponse = await refreshAccessToken(
				this.config.issuer,
				this.config.clientId,
				refreshToken,
			);

			await this.repository.updateTokens(tokenResponse);
			await this.repository.saveAuthState('authenticated');

			console.log('✅ [OktaAuthService] Token refreshed successfully');
			return true;
		} catch (error) {
			console.error('❌ [OktaAuthService] Failed to refresh token:', error);
			await this.repository.saveAuthState('token_expired');
			return false;
		}
	}

	/**
	 * Logout user and clear all authentication data
	 */
	async logout(): Promise<void> {
		try {
			console.log('🚪 [OktaAuthService] Logging out user...');

			await this.repository.clearAuthData();
			await this.repository.saveAuthState('unauthenticated');

			// Clear current PKCE parameters
			this.currentPKCE = null;

			console.log('✅ [OktaAuthService] Logout completed');
		} catch (error) {
			console.error('❌ [OktaAuthService] Failed to logout:', error);
			throw new OktaAuthenticationError(
				OktaAuthError.UNKNOWN_ERROR,
				'Failed to logout',
				error as Error,
			);
		}
	}

	/**
	 * Get current authentication state
	 */
	async getAuthState(): Promise<AuthenticationState> {
		try {
			const state = await this.repository.getAuthState();
			return state || 'unauthenticated';
		} catch (error) {
			console.error('❌ [OktaAuthService] Failed to get auth state:', error);
			return 'unauthenticated';
		}
	}

	/**
	 * Process authentication response with tokens already exchanged by expo-auth-session
	 * This is used when expo-auth-session automatically exchanges the code for tokens
	 */
	async processAuthenticationTokens(authentication: any): Promise<OktaAuthResult> {
		try {
			await this.repository.saveAuthState('authenticating');

			console.log(
				'🔄 [OktaAuthService] Processing pre-exchanged tokens from expo-auth-session...',
			);

			// Extract tokens from expo-auth-session authentication object
			const accessToken = authentication.accessToken;
			const refreshToken = authentication.refreshToken;
			const idToken = authentication.idToken;
			const tokenType = authentication.tokenType || 'Bearer';
			const expiresIn = authentication.expiresIn || 3600;
			const scope = authentication.scope;

			if (!accessToken) {
				throw new OktaAuthenticationError(
					OktaAuthError.INVALID_GRANT,
					'No access token in authentication response',
				);
			}

			// Parse user profile from ID token if available
			let userProfile: OktaUserProfile | undefined;
			if (idToken) {
				try {
					const idTokenClaims = parseIdToken(idToken);
					userProfile = extractUserProfile(idTokenClaims);
					console.log('✅ [OktaAuthService] User profile extracted from ID token');
				} catch (error) {
					console.warn('⚠️ [OktaAuthService] Failed to parse ID token:', error);
					// Continue without user profile - not critical
				}
			}

			// Create authentication result
			const authResult: OktaAuthResult = {
				success: true,
				accessToken,
				refreshToken,
				idToken,
				tokenType,
				expiresAt: Date.now() + expiresIn * 1000,
				scope,
				userProfile,
			};

			// Store authentication data
			await this.repository.saveAuthData(authResult);
			await this.repository.saveAuthState('authenticated');

			console.log(
				'✅ [OktaAuthService] Pre-exchanged token processing completed successfully',
			);

			return authResult;
		} catch (error) {
			await this.repository.saveAuthState('error');

			if (error instanceof OktaAuthenticationError) {
				throw error;
			}

			throw new OktaAuthenticationError(
				OktaAuthError.UNKNOWN_ERROR,
				'Failed to process pre-exchanged tokens',
				error as Error,
			);
		}
	}
	/**
	 * Update Okta configuration
	 */
	updateConfig(config: Partial<OktaConfig>): void {
		this.config = { ...this.config, ...config };
		console.log('🔧 [OktaAuthService] Configuration updated');
	}
}

/**
 * Create OktaAuthService instance with default configuration
 */
export function createOktaAuthService(
	repository?: OktaAuthRepository,
	config?: Partial<OktaConfig>,
): OktaAuthService {
	// Create repository instance if not provided
	if (!repository) {
		const { SecureStorageRepository } = require('@/repositories/secureStorageRepository');
		const storage = new SecureStorageRepository();
		repository = new OktaAuthRepository(storage);
	}

	const defaultConfig: OktaConfig = {
		issuer:
			process.env.EXPO_PUBLIC_OKTA_ISSUER ||
			'https://integrator-5743111.okta.com/oauth2/default',
		clientId: process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here',
		redirectUri: makeRedirectUri({
			scheme: 'learningcoachcommunity',
			path: 'callback',
		}),
		scopes: ['openid', 'profile', 'email'],
		...config,
	};

	return new OktaAuthService(repository, defaultConfig);
}

// Export singleton instance
export const oktaAuthService = createOktaAuthService();
